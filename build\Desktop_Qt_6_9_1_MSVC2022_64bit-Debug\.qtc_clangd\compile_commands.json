[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\findpassword.cpp"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/findpassword.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\initsurface.cpp"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/initsurface.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\main.cpp"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\login.cpp"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/login.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\publicchat.cpp"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/publicchat.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\register.cpp"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/register.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\selectuserinterface.cpp"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/selectuserinterface.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\user.cpp"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/user.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\findpassword.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/findpassword.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\initsurface.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/initsurface.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\login.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/login.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\publicchat.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/publicchat.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\register.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/register.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\selectuserinterface.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/selectuserinterface.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\user.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/user.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_publicchat.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_publicchat.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_selectuserinterface.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_selectuserinterface.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_login.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_login.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_findpassword.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_findpassword.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_user.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_user.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_register.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_register.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\OnlineChat-master\\ChatClient\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_initsurface.h"], "directory": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/OnlineChat-master/ChatClient/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_initsurface.h"}]