<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Login</class>
 <widget class="QWidget" name="Login">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Login</string>
  </property>
  <widget class="QFrame" name="Login_background">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>801</width>
     <height>601</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-image: url(:/image/Login_background.png);</string>
   </property>
   <property name="frameShape">
    <enum>QFrame::StyledPanel</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Raised</enum>
   </property>
   <widget class="QLabel" name="account_label">
    <property name="geometry">
     <rect>
      <x>150</x>
      <y>290</y>
      <width>51</width>
      <height>51</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">color:rgb(255, 255, 255);
font: 13pt &quot;宋体&quot;;</string>
    </property>
    <property name="text">
     <string>账号</string>
    </property>
   </widget>
   <widget class="QLabel" name="password_label">
    <property name="geometry">
     <rect>
      <x>150</x>
      <y>380</y>
      <width>51</width>
      <height>41</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">color:rgb(255, 255, 255);
font: 13pt &quot;宋体&quot;;</string>
    </property>
    <property name="text">
     <string>密码</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="account_Edit">
    <property name="geometry">
     <rect>
      <x>240</x>
      <y>300</y>
      <width>291</width>
      <height>41</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">color:rgb(255, 255, 255)</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="password_Edit">
    <property name="geometry">
     <rect>
      <x>240</x>
      <y>380</y>
      <width>291</width>
      <height>41</height>
     </rect>
    </property>
   </widget>
   <widget class="QPushButton" name="register_button">
    <property name="geometry">
     <rect>
      <x>560</x>
      <y>300</y>
      <width>131</width>
      <height>41</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">color:rgb(255, 255, 255);
font: 12pt &quot;宋体&quot;;</string>
    </property>
    <property name="text">
     <string>注册账号</string>
    </property>
   </widget>
   <widget class="QPushButton" name="lost_password_button">
    <property name="geometry">
     <rect>
      <x>560</x>
      <y>380</y>
      <width>131</width>
      <height>41</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">color:rgb(255, 255, 255);
font: 12pt &quot;宋体&quot;;</string>
    </property>
    <property name="text">
     <string>找回密码</string>
    </property>
   </widget>
   <widget class="QPushButton" name="login_button">
    <property name="geometry">
     <rect>
      <x>310</x>
      <y>470</y>
      <width>171</width>
      <height>51</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">color:rgb(255, 255, 255);
font: 20pt &quot;宋体&quot;;</string>
    </property>
    <property name="text">
     <string>登陆</string>
    </property>
   </widget>
   <widget class="QFrame" name="frame">
    <property name="geometry">
     <rect>
      <x>320</x>
      <y>120</y>
      <width>151</width>
      <height>161</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">image: url(:/image/img/QQ.png);</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
   </widget>
   <widget class="QLabel" name="tip_label">
    <property name="geometry">
     <rect>
      <x>600</x>
      <y>550</y>
      <width>171</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">color: rgb(255, 255, 255);
background-color: rgb(255, 255, 255);
font: 12pt &quot;宋体&quot;;</string>
    </property>
    <property name="text">
     <string>未输入账号</string>
    </property>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
