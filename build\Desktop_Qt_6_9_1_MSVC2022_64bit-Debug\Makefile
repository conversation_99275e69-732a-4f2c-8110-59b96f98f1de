#############################################################################
# Makefile for building: ChatClient
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  ..\..\ChatClient.pro
# Template: app
# Command: E:\YingYong\QT\6.9.1\msvc2022_64\bin\qmake.exe -o Makefile ..\..\ChatClient.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"
#############################################################################

MAKEFILE      = Makefile

EQ            = =

first: debug
install: debug-install
uninstall: debug-uninstall
QMAKE         = E:\YingYong\QT\6.9.1\msvc2022_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = E:\YingYong\QT\6.9.1\msvc2022_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = E:\YingYong\QT\6.9.1\msvc2022_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move
SUBTARGETS    =  \
		debug \
		release


debug: $(MAKEFILE) FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug uninstall
release: $(MAKEFILE) FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release uninstall

Makefile: ..\..\ChatClient.pro E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc\qmake.conf E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\spec_pre.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\windows-desktop.conf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\windows_vulkan_sdk.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\windows-vulkan.conf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\msvc-desktop.conf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\qconfig.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_freetype.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_libjpeg.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_libpng.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_openxr_loader.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_charts.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_charts_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_chartsqml.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_chartsqml_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_concurrent.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_concurrent_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_core.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_core_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_dbus.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_dbus_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designer.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designer_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designercomponents_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_entrypoint_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_example_icons_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_fb_support_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_freetype_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_gui.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_gui_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_harfbuzz_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_help.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_help_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_httpserver.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_httpserver_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_jpeg_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsanimation.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsanimation_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsplatform.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsplatform_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssettings.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssettings_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_linguist.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimedia.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimedia_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediaquick_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_network.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_network_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_opengl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_opengl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_packetprotocol_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_png_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_printsupport.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_printsupport_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatch_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qml.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qml_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcore.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcore_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmldebug_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmldom_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlformat_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlls_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltest.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltest_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3d.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3d_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickeffects.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickeffects_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickparticles_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickshapes_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_serialport.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_serialport_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_shadertools.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_shadertools_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_sql.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_sql_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svg.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svg_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testinternals_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testlib.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testlib_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_tools_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uiplugin.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uitools.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uitools_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_websockets.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_websockets_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_widgets.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_widgets_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_xml.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_xml_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_zlib_private.pri \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt_functions.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt_config.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc\qmake.conf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\spec_post.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exclusive_builds.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\msvc-version.conf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\toolchain.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\default_pre.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\default_pre.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resolve_config.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exclusive_builds_post.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\default_post.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qml_debug.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\precompile_header.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\warn_on.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\permissions.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resources_functions.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resources.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\moc.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\opengl.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\uic.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qmake_use.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\file_copies.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\windows.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\testcase_targets.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exceptions.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\yacc.prf \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\lex.prf \
		..\..\ChatClient.pro \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.prl \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Gui.prl \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Network.prl \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Core.prl \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6EntryPoint.prl \
		.qmake.stash \
		E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\build_pass.prf \
		..\..\Resources.qrc \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Widgetsd.prl \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Guid.prl \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Networkd.prl \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Cored.prl \
		E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6EntryPointd.prl
	$(QMAKE) -o Makefile ..\..\ChatClient.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\spec_pre.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\windows-desktop.conf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\windows_vulkan_sdk.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\windows-vulkan.conf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\msvc-desktop.conf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\qconfig.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_freetype.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_libjpeg.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_libpng.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_openxr_loader.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_charts.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_charts_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_chartsqml.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_chartsqml_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_concurrent.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_concurrent_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_core.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_core_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_dbus.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_dbus_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designer.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designer_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designercomponents_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_entrypoint_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_example_icons_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_fb_support_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_freetype_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_gui.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_gui_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_harfbuzz_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_help.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_help_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_httpserver.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_httpserver_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_jpeg_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsanimation.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsanimation_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsplatform.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsplatform_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssettings.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssettings_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_linguist.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimedia.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimedia_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediaquick_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_network.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_network_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_opengl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_opengl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_packetprotocol_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_png_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_printsupport.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_printsupport_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatch_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qml.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qml_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcore.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcore_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmldebug_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmldom_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlformat_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlls_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltest.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltest_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3d.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3d_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickeffects.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickeffects_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickparticles_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickshapes_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_serialport.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_serialport_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_shadertools.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_shadertools_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_sql.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_sql_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svg.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svg_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testinternals_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testlib.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testlib_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_tools_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uiplugin.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uitools.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uitools_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_websockets.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_websockets_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_widgets.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_widgets_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_xml.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_xml_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_zlib_private.pri:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt_functions.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt_config.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc\qmake.conf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\spec_post.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exclusive_builds.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\common\msvc-version.conf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\toolchain.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\default_pre.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\default_pre.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resolve_config.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exclusive_builds_post.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\default_post.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qml_debug.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\precompile_header.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\warn_on.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\permissions.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qt.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resources_functions.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\resources.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\moc.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\opengl.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\uic.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\qmake_use.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\file_copies.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\win32\windows.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\testcase_targets.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\exceptions.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\yacc.prf:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\lex.prf:
..\..\ChatClient.pro:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.prl:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Gui.prl:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Network.prl:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Core.prl:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6EntryPoint.prl:
.qmake.stash:
E:\YingYong\QT\6.9.1\msvc2022_64\mkspecs\features\build_pass.prf:
..\..\Resources.qrc:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Widgetsd.prl:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Guid.prl:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Networkd.prl:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6Cored.prl:
E:\YingYong\QT\6.9.1\msvc2022_64\lib\Qt6EntryPointd.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile ..\..\ChatClient.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

make_first: debug-make_first release-make_first  FORCE
all: debug-all release-all  FORCE
clean: debug-clean release-clean  FORCE
	-$(DEL_FILE) ChatClient.vc.pdb
	-$(DEL_FILE) ChatClient.ilk
	-$(DEL_FILE) ChatClient.idb
distclean: debug-distclean release-distclean  FORCE
	-$(DEL_FILE) Makefile
	-$(DEL_FILE) .qmake.stash ChatClient.pdb

debug-mocclean:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug mocclean
release-mocclean:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release mocclean
mocclean: debug-mocclean release-mocclean

debug-mocables:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug mocables
release-mocables:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release mocables
mocables: debug-mocables release-mocables

check: first

benchmark: first
FORCE:

$(MAKEFILE).Debug: Makefile
$(MAKEFILE).Release: Makefile
